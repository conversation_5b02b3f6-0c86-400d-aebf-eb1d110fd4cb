import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { InjectQueue } from '@nestjs/bull';
import { Model } from 'mongoose';
import { Queue } from 'bull';
import { Telegraf } from 'telegraf';
import { SyncDto } from '../dto/sync.dto';
import { SyncConfig } from '../schemas/sync-config.schema';
import { queues, jobs } from '../constant/mq.constant';

import * as dotenv from "dotenv";
dotenv.config();

@Injectable()
export class SyncService implements OnModuleInit {
  private bot: Telegraf;
  private isListening = false;
  private logger = new Logger(SyncService.name);

  constructor(
    @InjectModel(SyncConfig.name) private readonly syncConfigModel: Model<SyncConfig>,
    @InjectQueue(queues.MESSAGE_SYNC_QUEUE) private readonly syncQueue: Queue,
  ) {
    this.initializeBot();
  }

  private initializeBot() {
    const token = process.env.TELEGRAM_BOT_TOKEN;
    if (!token) {
      this.logger.error('❌ TELEGRAM_BOT_TOKEN 环境变量未设置');
      return;
    }

    this.bot = new Telegraf(token);
    this.setupBot();
  }

  async onModuleInit() {
    this.logger.log('🔄 SyncService 模块初始化开始...');
    try {
      // 检查必要的环境变量
      if (!process.env.TELEGRAM_BOT_TOKEN) {
        this.logger.warn('⚠️ 未配置TELEGRAM_BOT_TOKEN，Bot将不会启动');
        return;
      }

      // 延迟启动Bot，确保Redis和MongoDB连接就绪
      const startupDelay = process.env.NODE_ENV === 'production' ? 5000 : 2000;
      setTimeout(async () => {
        try {
          this.logger.log('🤖 开始启动Telegram Bot...');
          await this.startBotListening();
        } catch (error) {
          this.logger.error('❌ Bot启动过程中发生错误:', error);
          // 继续运行服务，Bot可后续手动重启
          this.logger.log('💡 服务将继续运行，Bot可后续通过API手动重启');
        }
      }, startupDelay);
      
      this.logger.log(`✅ SyncService 模块初始化完成 (Bot将在${startupDelay/1000}秒后启动)`);
    } catch (error) {
      this.logger.error('❌ SyncService 模块初始化失败:', error);
      // 不抛出错误，让应用继续启动
      this.logger.warn('⚠️ Bot启动失败，但应用将继续运行（Bot可后续手动重启）');
    }
  }

  async syncTelegramToGame(dto: SyncDto) {
    this.logger.log(`🔍 检查群组配置: telegramGroupId=${dto.chatId}`);
    const config = await this.syncConfigModel.findOne({ telegramGroupId: dto.chatId, enabled: true });
    if (!config) {
      this.logger.warn(`⚠️ 群组未配置或未启用: telegramGroupId=${dto.chatId}`);
      return { success: false, error: '群组未配置' };
    }

    const message = {
      messageId: dto.messageId,
      fromUser: dto.fromUser,
      content: dto.content,
      replyTo: dto.replyTo,
      isAdmin: dto.isAdmin || false
    };

    this.logger.log(`📝 将消息加入队列: ${jobs.TELEGRAM_TO_GAME}, messageId=${dto.messageId}`);
    await this.syncQueue.add(jobs.TELEGRAM_TO_GAME, { message, config });
    return { success: true };
  }

  async syncGameToTelegram(dto: SyncDto) {
    this.logger.log(`🔍 检查频道配置: gameChatChannelId=${dto.chatId}`);
    const config = await this.syncConfigModel.findOne({ gameChatChannelId: dto.chatId, enabled: true });
    if (!config) {
      this.logger.warn(`⚠️ 频道未配置或未启用: gameChatChannelId=${dto.chatId}`);
      return { success: false, error: '频道未配置' };
    }

    const message = {
      messageId: dto.messageId,
      fromUser: dto.fromUser,
      content: dto.content,
      replyTo: dto.replyTo
    };

    this.logger.log(`📝 将消息加入队列: ${jobs.GAME_TO_TELEGRAM}, messageId=${dto.messageId}`);
    await this.syncQueue.add(jobs.GAME_TO_TELEGRAM, { message, config });
    return { success: true };
  }

  async getConfigs() {
    const configs = await this.syncConfigModel.find({ enabled: true });
    return { success: true, data: configs };
  }

  async updateConfig(config: any) {
    await this.syncConfigModel.findOneAndUpdate(
      { telegramGroupId: config.telegramGroupId },
      config,
      { upsert: true, new: true }
    );
    return { success: true };
  }

  async restartBot() {
    this.logger.log('🔄 手动重启 Telegram Bot...');

    try {
      // 先停止现有Bot
      if (this.bot && this.isListening) {
        try {
          await this.bot.stop();
          this.isListening = false;
          this.logger.log('🛑 Bot 已停止');
          
          // 等待一小段时间确保完全停止
          await new Promise(resolve => setTimeout(resolve, 1000));
        } catch (error) {
          this.logger.warn('停止 Bot 时出错:', error);
        }
      }

      // 重新初始化并启动
      this.initializeBot();
      
      // 给初始化一点时间
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // 使用专门的启动方法，避免递归重试
      const result = await this.attemptBotLaunchWithRetry(1, 3); // 最多重试3次
      
      if (this.isListening) {
        return {
          success: true,
          message: 'Bot 重启成功',
          timestamp: new Date().toISOString(),
          botStatus: this.getBotStatus()
        };
      } else {
        return {
          success: false,
          message: 'Bot 重启失败，请稍后重试',
          timestamp: new Date().toISOString(),
          botStatus: this.getBotStatus()
        };
      }
      
    } catch (error) {
      const errorInfo = this.analyzeBotError(error);
      this.logger.error('重启Bot失败:', error);
      return {
        success: false,
        message: `Bot 重启失败: ${errorInfo.description}`,
        error: errorInfo,
        timestamp: new Date().toISOString(),
        botStatus: this.getBotStatus()
      };
    }
  }

  getBotStatus() {
    return {
      isListening: this.isListening,
      botToken: process.env.TELEGRAM_BOT_TOKEN ? '已配置' : '未配置',
      status: this.isListening ? 'running' : 'stopped',
      timestamp: new Date().toISOString(),
      lastCheck: new Date().toISOString(),
      health: {
        botInitialized: !!this.bot,
        tokenConfigured: !!process.env.TELEGRAM_BOT_TOKEN,
        serviceReady: true  // 服务总是准备就绪，即使Bot未启动
      }
    };
  }

  async clearBotConflicts() {
    this.logger.log('🔧 强制清除 Bot 冲突状态...');

    try {
      // 停止当前 Bot（如果在运行）
      if (this.bot && this.isListening) {
        try {
          this.bot.stop();
          this.isListening = false;
          this.logger.log('🛑 当前 Bot 实例已停止');
        } catch (error) {
          this.logger.warn('停止当前 Bot 时出现警告:', error);
        }
      }

      // 重新初始化 Bot
      this.initializeBot();

      if (this.bot) {
        // 清除 webhook 和 pending updates
        await this.bot.telegram.deleteWebhook({ drop_pending_updates: true });
        this.logger.log('✅ Webhook 和 pending updates 已清除');

        // 获取 Bot 信息验证连接
        const botInfo = await this.bot.telegram.getMe();
        this.logger.log(`✅ Bot 连接验证成功: @${(botInfo as any).username}`);

        return {
          success: true,
          message: 'Bot 冲突状态已清除',
          botInfo: {
            username: (botInfo as any).username,
            firstName: (botInfo as any).first_name,
            id: (botInfo as any).id
          }
        };
      } else {
        throw new Error('Bot 初始化失败');
      }

    } catch (error) {
      this.logger.error('❌ 清除 Bot 冲突失败:', error);
      return {
        success: false,
        message: `清除失败: ${error?.message || error}`
      };
    }
  }

  private setupBot() {
    this.bot.on('message', async (ctx) => {
      const message = ctx.message;
      const chatId = message.chat.id.toString();
      
      this.logger.log(`📱 收到Telegram消息: chatId=${chatId}, messageId=${message.message_id}`);
      
      const config = await this.syncConfigModel.findOne({ telegramGroupId: chatId, enabled: true });
      if (!config) {
        this.logger.warn(`⚠️ 未找到群组配置或群组未启用: chatId=${chatId}`);
        return;
      }

      // 只处理文本消息
      if ('text' in message) {
        const syncDto: SyncDto = {
          messageId: message.message_id.toString(),
          fromUser: message.from?.username || message.from?.first_name || 'Unknown',
          content: message.text || '',
          timestamp: message.date * 1000,
          chatId: chatId,
          threadId: (message as any).message_thread_id?.toString(),
          replyTo: (message as any).reply_to_message?.message_id?.toString(),
          
          isAdmin: false,
        };

        this.logger.log(`📤 准备同步消息到游戏: chatId=${chatId}, user=${syncDto.fromUser}, content="${syncDto.content.substring(0, 50)}${syncDto.content.length > 50 ? '...' : ''}"`);
        
        try {
          const result = await this.syncTelegramToGame(syncDto);
          if (result.success) {
            this.logger.log(`✅ 消息同步成功: messageId=${syncDto.messageId}`);
          } else {
            this.logger.error(`❌ 消息同步失败: ${result.error}`);
          }
        } catch (error) {
          this.logger.error(`❌ 消息同步异常:`, error);
        }
      } else {
        const messageType = this.getMessageType(message);
        this.logger.log(`ℹ️ 跳过非文本消息: messageId=${message.message_id}, type=${messageType}`);
      }
    });
  }

  private async startBotListening() {
    if (!this.bot) {
      this.logger.error('❌ Bot 未初始化，跳过启动');
      return;
    }

    const maxRetries = 5;
    let retryCount = 0;
    
    const attemptLaunch = async () => {
      try {
        this.logger.log(`🔄 正在启动 Telegram Bot... (尝试 ${retryCount + 1}/${maxRetries})`);
        this.logger.log(`🔑 Bot Token 已配置: ${process.env.TELEGRAM_BOT_TOKEN?.substring(0, 10)}...`);

        // 清除可能存在的 webhook 和 pending updates
        await this.clearBotState();

        // 使用更长的超时时间和分阶段启动
        // 阶段1：验证Bot连接
        const botInfo = await Promise.race([
          this.bot.telegram.getMe(),
          new Promise<never>((_, reject) => {
            setTimeout(() => reject(new Error('Bot连接验证超时 (30秒)')), 30000);
          })
        ]);
        
        this.logger.log(`✅ Bot连接验证成功: @${(botInfo as any).username} (${(botInfo as any).id})`);

        // 阶段2：启动Bot监听
        try {
          // 使用更安全的启动方式 - 先清除webhook
          await this.bot.telegram.deleteWebhook({ drop_pending_updates: true });
          
          // 使用polling方式启动
          await this.bot.launch({
            polling: {
              timeout: 30,
              limit: 100,
              allowedUpdates: ['message']
            }
          });
          
          // 给启动过程一些时间
          await new Promise(resolve => setTimeout(resolve, 2000));
          
          this.isListening = true;
          this.logger.log('🤖 Telegram Bot 启动成功，开始监听消息...');
          
          // 启动成功，重置重试计数
          retryCount = 0;
          return true;
          
        } catch (launchError) {
          // 如果启动失败，尝试更简单的启动方式
          this.logger.warn('⚠️ 标准启动方式失败，尝试简化启动...');
          
          // 强制清除状态后重试
          await this.clearBotState();
          await this.bot.telegram.deleteWebhook({ drop_pending_updates: true });
          
          // 使用基本配置启动
          await this.bot.launch();
          
          await new Promise(resolve => setTimeout(resolve, 1000));
          
          this.isListening = true;
          this.logger.log('🤖 Telegram Bot 简化启动成功');
          retryCount = 0;
          return true;
        }

      } catch (error) {
        const errorInfo = this.analyzeBotError(error);
        this.logger.error(`❌ Telegram Bot 启动失败 (尝试 ${retryCount + 1}/${maxRetries}): ${errorInfo.description}`);
        this.isListening = false;

        // 仅记录详细错误日志一次，避免重复
        if (retryCount === 0) {
          this.logger.debug('详细错误信息:', error);
        }

        // 针对特定错误类型的处理
        if (errorInfo.type === 'conflict') {
          this.logger.log('🔄 检测到 Bot 实例冲突，尝试强制清除状态...');
          try {
            await this.clearBotConflicts();
            this.logger.log('✅ 冲突状态已清除');
          } catch (clearError) {
            this.logger.warn('⚠️ 清除冲突状态时出错:', clearError.message);
          }
        }

        return false;
      }
    };

    // 初始尝试
    const success = await attemptLaunch();
    
    if (!success && retryCount < maxRetries) {
      this.scheduleRetryWithBackoff(++retryCount, maxRetries);
    } else if (!success && retryCount >= maxRetries) {
      this.logger.error(`❌ 已达到最大重试次数 (${maxRetries})，Bot启动失败`);
      // 继续运行服务，但标记Bot为不可用状态
      this.isListening = false;
    }
  }

  private async clearBotState() {
    try {
      this.logger.log('🧹 清除 Bot 状态...');

      // 删除 webhook（如果存在）
      await this.bot.telegram.deleteWebhook({ drop_pending_updates: true });
      this.logger.log('✅ Webhook 已清除');

    } catch (error) {
      this.logger.warn('⚠️ 清除 Bot 状态时出现警告:', error?.message || error);
      // 不抛出错误，继续启动流程
    }
  }

  private shouldRetry(error: any): boolean {
    // 检查是否是可重试的错误
    const retryableErrors = [
      'timeout',
      'ECONNRESET',
      'ENOTFOUND',
      'ECONNREFUSED',
      'ETIMEDOUT'
    ];

    const errorMessage = error?.message?.toLowerCase() || '';
    return retryableErrors.some(retryError => errorMessage.includes(retryError));
  }

  private scheduleRetry() {
    // 30秒后重试
    setTimeout(() => {
      if (!this.isListening) {
        this.logger.log('🔄 尝试重新启动 Telegram Bot...');
        this.startBotListening();
      }
    }, 30000);
  }

  private scheduleRetryWithBackoff(retryCount: number, maxRetries: number) {
    if (retryCount >= maxRetries) {
      this.logger.error(`❌ 已达到最大重试次数 (${maxRetries})，停止尝试启动 Bot`);
      return;
    }

    // 指数退避：2^retryCount * 5秒，最大5分钟
    const baseDelay = 5000; // 5秒
    const backoffMultiplier = Math.pow(2, retryCount - 1);
    const maxDelay = 300000; // 5分钟
    const delay = Math.min(baseDelay * backoffMultiplier, maxDelay);
    
    this.logger.log(`⏰ ${delay/1000}秒后自动重试 (第${retryCount + 1}/${maxRetries}次)...`);
    
    setTimeout(async () => {
      if (!this.isListening) {
        this.logger.log(`🔄 第${retryCount + 1}次重试启动 Telegram Bot...`);
        
        try {
          // 使用新的重试方法，避免递归调用startBotListening
          await this.attemptBotLaunchWithRetry(retryCount + 1, maxRetries);
        } catch (error) {
          this.logger.error(`❌ 第${retryCount + 1}次重试失败:`, error);
          this.scheduleRetryWithBackoff(retryCount + 1, maxRetries);
        }
      }
    }, delay);
  }

  private async attemptBotLaunchWithRetry(currentRetry: number, maxRetries: number) {
    try {
      this.logger.log(`🔄 重试启动Bot (第${currentRetry}/${maxRetries}次)...`);
      
      if (!this.bot) {
        // 如果bot未初始化，重新初始化
        this.initializeBot();
        await new Promise(resolve => setTimeout(resolve, 1000));
      }

      // 强制清除所有状态
      await this.clearBotConflicts();
      
      // 使用最简化的启动方式 - 先清除webhook
      await this.bot.telegram.deleteWebhook({ drop_pending_updates: true });
      
      // 使用polling方式启动
      await this.bot.launch({
        polling: {
          timeout: 30,
          limit: 10 // 减少limit以降低负载
        }
      });
      
      // 验证启动成功
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      if (this.bot) {
        const botInfo = await this.bot.telegram.getMe();
        this.logger.log(`✅ 重试启动成功: @${(botInfo as any).username}`);
        this.isListening = true;
      }
      
    } catch (error) {
      this.logger.error(`❌ 重试启动失败:`, error);
      if (currentRetry < maxRetries) {
        this.scheduleRetryWithBackoff(currentRetry, maxRetries);
      }
    }
  }

  private analyzeBotError(error: any): { type: string; description: string; retryable: boolean } {
    const errorMessage = error?.message || error?.description || error?.toString() || 'Unknown error';
    const errorCode = error?.response?.error_code || error?.error_code;
    
    // 错误类型映射
    const errorPatterns = [
      {
        type: 'conflict',
        patterns: ['409', 'conflict', 'terminated', 'webhook', 'another instance'],
        description: 'Bot实例冲突：另一个Bot实例正在运行'
      },
      {
        type: 'network',
        patterns: ['timeout', 'ECONNRESET', 'ENOTFOUND', 'ECONNREFUSED', 'ETIMEDOUT', 'network'],
        description: '网络连接问题'
      },
      {
        type: 'auth',
        patterns: ['401', '403', 'unauthorized', 'invalid token', 'authentication'],
        description: '认证失败：无效的Bot Token'
      },
      {
        type: 'rate_limit',
        patterns: ['429', 'rate limit', 'too many requests'],
        description: '请求频率限制'
      },
      {
        type: 'api_error',
        patterns: ['400', 'bad request', 'invalid parameter'],
        description: 'API参数错误'
      }
    ];

    const lowerErrorMessage = errorMessage.toLowerCase();
    
    for (const pattern of errorPatterns) {
      if (pattern.patterns.some(p => lowerErrorMessage.includes(p.toLowerCase()))) {
        return {
          type: pattern.type,
          description: pattern.description,
          retryable: pattern.type !== 'auth' && pattern.type !== 'api_error'
        };
      }
    }

    // 根据错误码判断
    if (errorCode) {
      switch (errorCode) {
        case 409:
          return { type: 'conflict', description: 'Bot实例冲突', retryable: true };
        case 401:
        case 403:
          return { type: 'auth', description: '认证失败', retryable: false };
        case 429:
          return { type: 'rate_limit', description: '请求频率限制', retryable: true };
        case 400:
          return { type: 'api_error', description: 'API参数错误', retryable: false };
        default:
          return { type: 'unknown', description: `未知错误 (代码: ${errorCode})`, retryable: true };
      }
    }

    return { type: 'unknown', description: errorMessage, retryable: true };
  }

    // 添加辅助方法来确定消息类型
  private getMessageType(message: any): string {
    if ('text' in message) return 'text';
    if ('photo' in message) return 'photo';
    if ('video' in message) return 'video';
    if ('audio' in message) return 'audio';
    if ('voice' in message) return 'voice';
    if ('document' in message) return 'document';
    if ('sticker' in message) return 'sticker';
    if ('animation' in message) return 'animation';
    if ('poll' in message) return 'poll';
    if ('location' in message) return 'location';
    return 'unknown';
  }
}
