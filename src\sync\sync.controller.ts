import { Controller, Post, Get, Body, Param } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { SyncDto } from '../dto/sync.dto';
import { SyncService } from './sync.service';

@ApiTags('sync')
@Controller('sync')
export class SyncController {
  constructor(private readonly syncService: SyncService) {}

  /**
   * Telegram消息同步到游戏
   */
  @Post('telegram-to-game')
  @ApiOperation({
    summary: '同步Telegram消息到游戏',
    description: '将Telegram群组消息同步到对应的游戏聊天频道',
  })
  @ApiResponse({ status: 201, description: '同步成功' })
  @ApiResponse({ status: 400, description: '参数错误' })
  async syncTelegramToGame(@Body() dto: SyncDto) {
    return await this.syncService.syncTelegramToGame(dto);
  }

  /**
   * 游戏消息同步到Telegram
   */
  @Post('game-to-telegram')
  @ApiOperation({
    summary: '同步游戏消息到Telegram',
    description: '将游戏聊天消息同步到对应的Telegram群组',
  })
  @ApiResponse({ status: 201, description: '同步成功' })
  @ApiResponse({ status: 400, description: '参数错误' })
  async syncGameToTelegram(@Body() dto: SyncDto) {
    return await this.syncService.syncGameToTelegram(dto);
  }


  /**
   * 获取同步配置
   */
  @Get('config')
  @ApiOperation({ summary: '获取所有同步配置' })
  async getConfigs() {
    return await this.syncService.getConfigs();
  }

  /**
   * 更新同步配置
   */
  @Post('config')
  @ApiOperation({ summary: '更新同步配置' })
  async updateConfig(@Body() config: any) {
    return await this.syncService.updateConfig(config);
  }

  /**
   * 重启 Telegram Bot
   */
  @Post('restart-bot')
  @ApiOperation({
    summary: '重启 Telegram Bot',
    description: '手动重启 Telegram Bot 连接，用于解决网络连接问题'
  })
  @ApiResponse({ status: 200, description: '重启成功' })
  async restartBot() {
    return await this.syncService.restartBot();
  }

  /**
   * 获取 Bot 状态
   */
  @Get('bot-status')
  @ApiOperation({
    summary: '获取 Telegram Bot 状态',
    description: '查看 Bot 当前运行状态和配置信息'
  })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getBotStatus() {
    return this.syncService.getBotStatus();
  }

  /**
   * 清除 Bot 冲突状态
   */
  @Post('clear-bot-conflicts')
  @ApiOperation({
    summary: '清除 Telegram Bot 冲突状态',
    description: '强制清除 Bot 的 webhook 和 pending updates，解决 409 冲突错误'
  })
  @ApiResponse({ status: 200, description: '清除成功' })
  async clearBotConflicts() {
    return await this.syncService.clearBotConflicts();
  }
}
